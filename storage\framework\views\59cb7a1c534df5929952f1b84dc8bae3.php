
<?php $__env->startSection('content'); ?>
<section class="setting" id="setting1">
    <div class="setting-wrapper">
        <?php echo $__env->make('layouts.admin.nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <div class="setting_content">
            <section class="about" id="about">
                <div class="titlebar">
                    <h1>Social Medias</h1>
                </div>
                <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <div class="social-wrapper">
                        <div class="card">
                            <div class="social_table-heading">
                                <p>Link</p>
                                <p>Icon</p>
                                <p></p>
                            </div>

                            <?php $__currentLoopData = $medias; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="social_table-items">
                                <p><?php echo e($media->link); ?></p>
                                <button class="service_table-icon">
                                    <i class="uil uil-<?php echo e($media->icon); ?>"></i>
                                </button>
                                <form method="POST" action="<?php echo e(route('admin.medias.destroy', $media->id)); ?>" style="display: inline;" class="delete-form">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="button" class="btn-icon danger delete-btn" data-experience-name="<?php echo e($media->link); ?>">
                                        Delete &nbsp;
                                        <i class="far fa-trash-alt"></i>
                                    </button>
                                </form>
                                <!-- <form method="POST" action="<?php echo e(route('admin.medias.destroy', $media->id)); ?>" style="display: inline-block;">
                                    <?php echo e(method_field('DELETE')); ?>

                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="danger" onclick="return confirm('Are you sure you want to delete this media?')">
                                        Delete
                                    </button>
                                </form> -->
                            </div> 
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <br>
                            <?php echo $__env->make('admin.medias.form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/admin/medias/index.blade.php ENDPATH**/ ?>