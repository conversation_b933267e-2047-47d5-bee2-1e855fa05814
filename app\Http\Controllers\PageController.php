<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\About;
use App\Models\Media;
use App\Models\Project;
use App\Models\Experience;

class PageController extends Controller
{
    public function index()
    {
        $abouts = About::orderBy('id', 'DESC')->get();
        $mediasHome = Media::orderBy('id', 'DESC')->get();
        $projectCount = Project::all()->count();
        $experiencesCount = Experience::all()->count();


        return view('pages.home.index',compact(
            [
                'abouts',
                'mediasHome',
                'projectCount',
                'experiencesCount'
                
            ]
        ));
    }
}
